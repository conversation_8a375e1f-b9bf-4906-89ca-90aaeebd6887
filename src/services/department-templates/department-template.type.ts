import { type ApiResponse } from '@/types/api-responses.type';
import type { DepartmentStatus } from '../departments';

export type DepartmentTemplateStatus = DepartmentStatus;

export type DepartmentTemplateScope = 'organisation' | 'merchant';

export interface DepartmentTemplate {
  id: string;

  name: string;
  description?: string;
  scope: DepartmentTemplateScope;

  status?: DepartmentTemplateStatus;

  createdBy?: string;
  createdDate: Date;
  updatedBy?: string;
  updatedDate: Date;
  [key: string]: any;
}

/**
 * Filter parameters for department templates
 */
export interface DepartmentTemplateFilterParams {
  filters: {
    search: string;
    scope: DepartmentTemplateScope | 'all';
    status: string[];
    updatedDate: Date | null;
    createdDate: Date | null;
  };
}

/**
 * Department template service interface
 */
export interface DepartmentTemplateService {
  /**
   * Get filtered users based on provided filters
   * @param filters - Filter parameters
   * @returns Promise with filtered users and error state
   */
  getFilteredDepartmentTemplates(
    filters: DepartmentTemplateFilterParams
  ): Promise<ApiResponse<DepartmentTemplate[]>>;
}
