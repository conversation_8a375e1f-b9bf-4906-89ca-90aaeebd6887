import { type ApiResponse } from '@/types/api-responses.type';

export type DepartmentStatus = 'active' | 'inactive';

export interface Department {
  id: string;

  name: string;
  description?: string;

  status?: DepartmentStatus;

  createdBy?: string;
  createdDate: Date;
  updatedBy?: string;
  updatedDate: Date;
  [key: string]: any;
}

/**
 * Filter parameters for departments
 */
export interface DepartmentFilterParams {
  filters: {
    search: string;
    status: string[];
    updatedDate: Date | null;
    createdDate: Date | null;
  };
}

/**
 * Department service interface
 */
export interface DepartmentService {
  /**
   * Get filtered users based on provided filters
   * @param filters - Filter parameters
   * @returns Promise with filtered users and error state
   */
  getFilteredDepartments(filters: DepartmentFilterParams): Promise<ApiResponse<Department[]>>;
}
