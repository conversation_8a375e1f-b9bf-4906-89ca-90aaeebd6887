import type { GridRenderCellParams, GridTreeNodeWithRender } from '@mui/x-data-grid-premium';
import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import {
  createColumns,
  formatDate,
  getDescriptionColumn,
  getNameColumn,
} from './DepartmentDataGrid.util';
import '@testing-library/jest-dom';
import { createHeaderName } from '@/components/base/data-display/datagrid';
import i18n from '@/providers/i18n/i18n';
import type { Department, DepartmentStatus } from '@/services/departments';
import type { Theme } from '@mui/material';

/**
 * Generates a mock object resembling MUI DataGrid's `GridRenderCellParams`
 * to be used in unit tests for column `renderCell` functions.
 *
 * @param {Object} params - The parameters for mocking cell render behavior.
 * @param {string} params.field - The field name of the column.
 * @param {any} params.value - The value of the cell to be rendered.
 *
 * @returns {GridRenderCellParams} A mock `GridRenderCellParams` object with essential properties for testing.
 *
 * @example
 * const params = getMockCellRenderParams({ field: 'status', value: 'active' });
 * const cell = statusColumn.renderCell(params);
 */
const getMockCellRenderParams = <
  RowType extends Department,
  FieldKey extends keyof RowType,
>(params?: {
  field: FieldKey;
  value: RowType[FieldKey];
}) => {
  const field = params?.field ?? '_UNKNOWN_FIELD';
  const value = params?.value;

  const mockGridTreeNodeWithRender: GridTreeNodeWithRender = {
    id: '1',
    depth: 0,
    type: 'leaf',
    parent: '1',
    groupingKey: null,
  };

  return {
    id: 1,
    value,
    row: {
      id: '1',
      name: 'Test Department',
      description: 'Test Description',
      status: 'active' as DepartmentStatus,
      createdDate: new Date(),
      updatedDate: new Date(),
      [field]: value,
    },
    field,
    formattedValue: value,
    isEditable: false,
    hasFocus: false,
    tabIndex: 0,
    api: {} as GridRenderCellParams['api'],
    getValue: () => value,
    cellMode: 'view',
    colDef: {
      computedWidth: 0,
      field,
      getActions: () => [],
    },
    rowNode: mockGridTreeNodeWithRender,
  } as GridRenderCellParams;
};

const mockTheme = {} as Theme;
const t = i18n.t;

describe('DepartmentDataGrid.util', () => {
  it('getNameColumn', () => {
    expect(getNameColumn(t)).toEqual({
      field: 'name',
      flex: 1,
      groupable: false,
      headerName: createHeaderName('name', t),
    });
  });

  it('getDescriptionColumn', () => {
    expect(getDescriptionColumn(t)).toEqual({
      field: 'description',
      flex: 2,
      groupable: false,
      minWidth: 140,
      headerName: createHeaderName('description', t),
    });
  });

  describe('createColumns', () => {
    const duplicateDepartment = vi.fn().mockImplementation((id) => () => `Duplicated ${id}`);

    const columns = createColumns({ duplicateDepartment }, mockTheme, t);

    it('should return the correct columns', () => {
      expect(columns.length).toBe(6);

      expect(columns[0]).toEqual(getNameColumn(t));
      expect(columns[1]).toEqual(getDescriptionColumn(t));
    });

    it('should define the column header correctly', () => {
      const nameCol = columns.find((col) => col.field === 'name');
      expect(nameCol?.headerName).toBe(createHeaderName('name', t));

      const descriptionCol = columns.find((col) => col.field === 'description');
      expect(descriptionCol?.headerName).toBe(createHeaderName('description', t));

      const statusCol = columns.find((col) => col.field === 'status');
      expect(statusCol?.headerName).toBe(createHeaderName('status', t));

      const createdDateCol = columns.find((col) => col.field === 'createdDate');
      expect(createdDateCol?.headerName).toBe(createHeaderName('createdDate', t));

      const updatedDateCol = columns.find((col) => col.field === 'updatedDate');
      expect(updatedDateCol?.headerName).toBe(createHeaderName('updatedDate', t));
    });

    describe('status cell', () => {
      const statusColumn = columns.find((col) => col.field === 'status');

      expect(statusColumn).toBeDefined();

      const cellRenderParamsValueExists = getMockCellRenderParams({
        field: 'status',
        value: 'active',
      });
      const cellRenderParamsValueUndefined = getMockCellRenderParams({
        field: 'status',
        value: undefined,
      });

      it('should render status cell correctly', () => {
        expect(statusColumn?.renderCell?.(cellRenderParamsValueExists)).toBeDefined();
        expect(statusColumn?.renderCell?.(cellRenderParamsValueUndefined)).toBeNull();
      });
    });

    describe('createdDate cell/header', () => {
      const createdDateColumn = columns.find((col) => col.field === 'createdDate');
      expect(createdDateColumn).toBeDefined();

      const cellRenderParams = getMockCellRenderParams({
        field: 'createdDate',
        value: new Date(),
      });

      it('should render createdDate cell correctly', () => {
        expect(createdDateColumn?.renderCell?.(cellRenderParams)).toBeDefined();
      });

      it('should have correct header', () => {
        const header = createdDateColumn?.renderHeader?.({
          field: 'createdDate',
          colDef: cellRenderParams.colDef,
        });
        render(header);
        const el = screen.getByTestId('createdDate-header-cell');
        expect(el).toBeInTheDocument();
      });
    });

    describe('updatedDate cell/header', () => {
      const updatedDateColumn = columns.find((col) => col.field === 'updatedDate');

      expect(updatedDateColumn).toBeDefined();
      const cellRenderParams = getMockCellRenderParams({
        field: 'updatedDate',
        value: new Date(),
      });

      it('should render updatedDate cell correctly', () => {
        expect(updatedDateColumn?.renderCell?.(cellRenderParams)).toBeDefined();
      });

      it('should have correct header', () => {
        const header = updatedDateColumn?.renderHeader?.({
          field: 'updatedDate',
          colDef: cellRenderParams.colDef,
        });
        render(header);
        const el = screen.getByTestId('updatedDate-header-cell');
        expect(el).toBeInTheDocument();
      });
    });

    describe('actions cell', () => {
      const actionsColumn = columns.find((col) => col.field === 'actions');
      expect(actionsColumn).toBeDefined();

      const cellRenderParams = getMockCellRenderParams();

      it('should have action items', async () => {
        // this is asserted above
        if ('getActions' in actionsColumn!) {
          const actions = actionsColumn?.getActions(cellRenderParams.row);
          expect(actions.length).toBeGreaterThan(0);
        }
      });

      it('should call duplicateDepartment function with correct id when duplicate action is triggered', () => {
        const duplicateDepartmentMock = vi.fn();
        const columns = createColumns(
          { duplicateDepartment: duplicateDepartmentMock },
          mockTheme,
          t
        );
        const actionsColumn = columns.find((col) => col.field === 'actions');
        expect(actionsColumn).toBeDefined();
      
        const cellRenderParams = getMockCellRenderParams({ field: 'id', value: 'test-id' });
      
        if ('getActions' in actionsColumn!) {
          const actions = actionsColumn.getActions(cellRenderParams.row);
          const duplicateAction = actions.find((action) =>
            typeof action.props.label === 'string' && action.props.label.includes('Duplicate')
          );
      
          expect(duplicateAction).toBeDefined();
      
          if (duplicateAction) {
            const clickEvent = new MouseEvent('click', { bubbles: true, cancelable: true });
            duplicateAction.props.onClick(clickEvent);
            expect(duplicateDepartmentMock).toHaveBeenCalledWith('test-id');
          }
        }
      });
    });
  });

  describe('formatDate', () => {
    it('should return a formatted date string', () => {
      expect(typeof formatDate(new Date())).toBe('string');
      expect(typeof formatDate('2025-01-01')).toBe('string');
    });
  });
});
