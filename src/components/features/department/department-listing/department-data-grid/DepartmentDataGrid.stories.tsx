'use client';

import i18n from '@/providers/i18n/i18n';
import type { Department } from '@/services/departments';
import { transformText } from '@/utils';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { t } from 'i18next';
import { expect, fn, screen, userEvent, waitFor } from 'storybook/test';
import DepartmentDataGrid from './DepartmentDataGrid';

const mockDepartments: Department[] = [
  {
    id: 'dpt-001',
    name: 'Finance',
    accessId: 'acc-001',
    description: 'Handles all financial operations',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2023-01-10T09:00:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-03-15T12:30:00Z'),
  },
  {
    id: 'dpt-002',
    name: 'Human Resources',
    accessId: 'acc-002',
    description: 'Manages recruitment and employee welfare',
    status: 'active',
    createdBy: '<PERSON>',
    createdDate: new Date('2023-02-05T10:15:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-01-22T11:45:00Z'),
  },
  {
    id: 'dpt-003',
    name: 'Marketing',
    accessId: 'acc-003',
    description: 'Oversees campaigns and promotions',
    status: 'inactive',
    createdBy: 'John',
    createdDate: new Date('2023-03-12T14:22:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-04-10T16:50:00Z'),
  },
  {
    id: 'dpt-004',
    name: 'IT Support',
    accessId: 'acc-004',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2023-04-08T08:40:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-05-20T09:30:00Z'),
  },
  {
    id: 'dpt-005',
    name: 'Legal',
    accessId: 'acc-005',
    description: 'Handles legal affairs and compliance',
    status: 'inactive',
    createdBy: 'John',
    createdDate: new Date('2023-05-19T07:55:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-02-25T10:10:00Z'),
  },
  {
    id: 'dpt-006',
    name: 'Sales',
    accessId: 'acc-006',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2023-06-30T13:25:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-07-01T11:20:00Z'),
  },
  {
    id: 'dpt-007',
    name: 'Customer Service',
    accessId: 'acc-007',
    description: 'Supports customer inquiries',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2023-07-14T09:10:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-06-18T08:15:00Z'),
  },
  {
    id: 'dpt-008',
    name: 'Product',
    accessId: 'acc-008',
    description: 'Responsible for product development',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2023-08-20T11:45:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-06-12T15:30:00Z'),
  },
  {
    id: 'dpt-009',
    name: 'Operations',
    accessId: 'acc-009',
    status: 'inactive',
    createdBy: 'John',
    createdDate: new Date('2023-09-05T06:35:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-03-30T12:00:00Z'),
  },
  {
    id: 'dpt-010',
    name: 'Security',
    accessId: 'acc-010',
    description: 'Ensures data and personnel safety',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2023-10-13T15:55:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-05-02T13:40:00Z'),
  },
  {
    id: 'dpt-011',
    name: 'Logistics',
    accessId: 'acc-011',
    status: 'inactive',
    createdBy: 'John',
    createdDate: new Date('2023-11-22T07:30:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-01-05T08:45:00Z'),
  },
  {
    id: 'dpt-012',
    name: 'Procurement',
    accessId: 'acc-012',
    description: 'Manages purchase and supplier relations',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2023-12-10T10:05:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-02-11T09:20:00Z'),
  },
  {
    id: 'dpt-013',
    name: 'Admin',
    accessId: 'acc-013',
    status: 'inactive',
    createdBy: 'John',
    createdDate: new Date('2024-01-15T12:15:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-03-05T14:10:00Z'),
  },
  {
    id: 'dpt-014',
    name: 'Training',
    accessId: 'acc-014',
    description: 'Conducts employee training programs',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2024-02-20T13:40:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-06-09T11:05:00Z'),
  },
  {
    id: 'dpt-015',
    name: 'Engineering',
    accessId: 'acc-015',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2024-03-14T08:25:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-07-01T07:55:00Z'),
  },
  {
    id: 'dpt-016',
    name: 'R&D',
    accessId: 'acc-016',
    description: 'Research and development division',
    status: 'inactive',
    createdBy: 'John',
    createdDate: new Date('2024-04-18T16:10:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-05-25T10:50:00Z'),
  },
  {
    id: 'dpt-017',
    name: 'Partnerships',
    accessId: 'acc-017',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2024-05-09T10:00:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-07-28T12:20:00Z'),
  },
  {
    id: 'dpt-018',
    name: 'Analytics',
    accessId: 'acc-018',
    description: 'Data and analytics',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2024-06-22T15:00:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-07-10T09:10:00Z'),
  },
  {
    id: 'dpt-019',
    name: 'Design',
    accessId: 'acc-019',
    status: 'inactive',
    createdBy: 'John',
    createdDate: new Date('2024-07-05T11:45:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2024-07-25T13:15:00Z'),
  },
  {
    id: 'dpt-020',
    name: 'Content',
    accessId: 'acc-020',
    description: 'Creates and manages content',
    status: 'active',
    createdBy: 'John',
    createdDate: new Date('2024-07-28T09:30:00Z'),
    updatedBy: 'Emily',
    updatedDate: new Date('2025-07-28T10:00:00Z'),
  },
];

const onDuplicateDepartmentMock = fn();

const meta: Meta<typeof DepartmentDataGrid> = {
  title: 'Components/features/department-listing/department-data-grid/DepartmentDataGrid ',
  component: DepartmentDataGrid,
  parameters: {
    layout: 'fullscreen',
  },

  args: {
    departments: mockDepartments,
    onDuplicateDepartment: onDuplicateDepartmentMock,
  },

  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);
      return (
        <div style={{ width: '1200px', margin: '0 auto' }}>
          <Story {...context} />
        </div>
      );
    },
  ],
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ step }) => {
    await step('Should render correct columns', async () => {
      expect(document.querySelector('[data-field=name]')).toBeInTheDocument();
      expect(document.querySelector('[data-field=description]')).toBeInTheDocument();
      expect(document.querySelector('[data-field=status]')).toBeInTheDocument();
      expect(document.querySelector('[data-field=createdDate]')).toBeInTheDocument();
      expect(document.querySelector('[data-field=updatedDate]')).toBeInTheDocument();
      expect(document.querySelector('[data-field=actions]')).toBeInTheDocument();
    });

    await step('Should render rows', async () => {
      const rows = document.querySelectorAll('[role=rowgroup] [data-rowindex][role=row]');
      expect(rows.length).toBeGreaterThan(0);
    });

    await step('Should able to perform action', async () => {
      const actionButton = document.querySelector('[role=row] button[role=menuitem]');
      expect(actionButton).toBeInTheDocument();

      if (!actionButton) {
        throw new Error('Action button not found');
      }
      await userEvent.click(actionButton);

      const duplicateButton = await waitFor(() =>
        screen.getByText(transformText(t('common.action.duplicate'), 'sentenceCase'))
      );
      expect(duplicateButton).toBeInTheDocument();

      await userEvent.click(duplicateButton);
      expect(onDuplicateDepartmentMock).toBeCalledTimes(1);
    });
  },
};
