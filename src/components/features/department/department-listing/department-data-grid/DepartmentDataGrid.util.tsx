import { createHeaderName } from '@/components/base/data-display/datagrid/DataGrid.util';
import { Label } from '@/components/base/data-display/label';
import { STATUS_LABEL_MAP } from '@/constants/status-labels.constant';
import transformText from '@/utils/text-transform.util';
import { Box } from '@mui/material';
import {
  GridActionsCellItem,
  type GridActionsColDef,
  type GridColDef,
} from '@mui/x-data-grid-premium';
import { format } from 'date-fns';
import type { TFunction } from 'i18next';
import type { CreateColumnsFn } from './DepartmentDataGrid.type';

/**
 * Returns the column definition for the "name" field.
 *
 * @param {TFunction} t - Translation function used to internationalize the header name.
 * @returns {GridColDef} Column configuration for the "name" field.
 */
export const getNameColumn = (t: TFunction): GridColDef => {
  return {
    field: 'name',
    flex: 1,
    groupable: false,
    headerName: createHeaderName('name', t),
  };
};

/**
 * Returns the column definition for the "description" field.
 *
 * @param {TFunction} t - Translation function used to internationalize the header name.
 * @returns {GridColDef} Column configuration for the "description" field.
 */
export const getDescriptionColumn = (t: TFunction): GridColDef => {
  return {
    field: 'description',
    flex: 2,
    groupable: false,
    minWidth: 140,
    headerName: createHeaderName('description', t),
  };
};

/**
 * Returns the column definition for the "status" field with a custom label renderer.
 *
 * The column supports "active" and "inactive" values, and renders them using the `Label` component.
 *
 * @param {TFunction} t - Translation function used to provide localized labels and header name.
 * @returns {GridColDef} Column configuration for the "status" field with a custom renderer.
 */
export const getStatusColumn = (t: TFunction): GridColDef => {
  return {
    field: 'status',
    flex: 1,
    type: 'singleSelect',
    valueOptions: [
      { label: t('common.label.active'), value: 'active' },
      { label: t('common.label.inactive'), value: 'inactive' },
    ],
    groupable: false,
    headerName: createHeaderName('status', t),
    renderCell: (params) =>
      params.row.status ? (
        <Label
          value={params.row.status}
          map={STATUS_LABEL_MAP}
          data-testId="status-label"
        />
      ) : null,
  };
};

export const getAuditColumn = ({
  type,
  t,
}: {
  type: 'created' | 'updated';
  t: TFunction;
}): GridColDef | GridActionsColDef => {
  const field = `${type}Date`;
  const byField = `${type}By`;

  return {
    field,
    type: 'dateTime',
    flex: 1,
    groupable: false,
    minWidth: 160,
    headerName: createHeaderName(field, t),
    cellClassName: 'no-line-height',
    renderHeader: () => (
      <span data-testid={`${field}-header-cell`}>
        {createHeaderName(byField, t)} / {createHeaderName('date', t, 'lowercase')}
      </span>
    ),
    renderCell: (params) => (
      <Box>
        <div>{params.row[byField]}</div>
        <div>{formatDate(params.row[field])}</div>
      </Box>
    ),
  };
};

/**
 * Creates the column definitions for the DepartmentDataGrid
 *
 * @param actions - Object of functions for creating actions (e.g., duplicateDepartment)
 * @param theme - The current theme
 * @param t - Translation function
 * @returns Array of column definitions
 */
export const createColumns: CreateColumnsFn = ({ duplicateDepartment }, _theme, t) => [
  getNameColumn(t),
  getDescriptionColumn(t),
  getStatusColumn(t),
  getAuditColumn({
    t,
    type: 'created',
  }),
  getAuditColumn({
    t,
    type: 'updated',
  }),
  {
    field: 'actions',
    type: 'actions',
    description: createHeaderName('actions', t),
    flex: 0.1,
    hideable: false,
    width: 40,
    getActions: (params) => [
      <GridActionsCellItem
        key={params.id}
        label={transformText(t('common.action.viewDetail'), 'sentenceCase')}
        showInMenu
      />,
      <GridActionsCellItem
        key={params.id}
        label={transformText(t('common.action.duplicate'), 'sentenceCase')}
        showInMenu
        onClick={() => {
          duplicateDepartment(params.row.id)();
        }}
      />,
    ],
  },
];

export const formatDate = (date: string | number | Date) => {
  return format(date, 'yyyy MM dd HH:mm:ss');
};
