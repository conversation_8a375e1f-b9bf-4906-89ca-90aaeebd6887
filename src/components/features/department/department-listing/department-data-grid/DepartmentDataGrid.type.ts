import type { Department } from '@/services/departments';
import type { Theme } from '@mui/material';
import type { GridActionsColDef, GridColDef } from '@mui/x-data-grid-premium';
import type { TFunction } from 'i18next';

/**
 * Props for the DepartmentDataGrid component
 */
export interface DepartmentDataGridProps {
  /**
   * Array of department data to display in the grid
   */
  departments: Department[];

  /**
   * Whether the data is currently loading
   */
  isLoading: boolean;

  /**
   * Callback fired when a department is duplicated
   *
   * If not provided, action will do nothing.
   * In a real application, this would typically make an API call to duplicate the department.
   *
   * @param id - The ID of the department to duplicate
   */
  onDuplicateDepartment: (id: string | number) => void;
}

export interface CreateColumnsActions {
  duplicateDepartment: (id: string | number) => () => void;
}

export type CreateColumnsFn = (
  actions: CreateColumnsActions,
  theme: Theme,
  t: TFunction<'translation', undefined>
) => (GridColDef<Department> | GridActionsColDef<Department>)[];
