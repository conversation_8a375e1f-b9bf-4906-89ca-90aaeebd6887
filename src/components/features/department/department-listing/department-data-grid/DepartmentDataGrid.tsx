import { DataGrid } from '@/components/base/data-display/datagrid';
import { useTheme } from '@mui/material';
import type { GridRowId } from '@mui/x-data-grid-premium';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { INITIAL_STATE, TOOLBAR_PROPS } from './DepartmentDataGrid.constant';
import type { DepartmentDataGridProps } from './DepartmentDataGrid.type';
import { createColumns } from './DepartmentDataGrid.util';

/**
 * DepartmentDataGrid component displays department data in a grid with filtering, sorting, and pagination.
 *
 * @example
 * ```tsx
 * <DepartmentDataGrid
 *   departments={departments}
 *   isLoading={isLoading}
 *   onDuplicateDepartment={handleDuplicateDepartment}
 * />
 * ```
 */
export const DepartmentDataGrid = ({
  departments,
  isLoading,
  onDuplicateDepartment,
}: DepartmentDataGridProps) => {
  const theme = useTheme();
  const { t } = useTranslation();

  const duplicateDepartment = useCallback(
    (id: GridRowId) => () => {
      if (onDuplicateDepartment) {
        onDuplicateDepartment(id);
      }
    },
    [onDuplicateDepartment]
  );

  // Memoize columns definition to prevent recreation on every render
  const columns = useMemo(
    () =>
      createColumns(
        {
          duplicateDepartment,
        },
        theme,
        t
      ),
    [duplicateDepartment, theme, t]
  );

  // Memoize initial state to prevent recreation
  const initialState = useMemo(() => INITIAL_STATE, []);

  return (
    <DataGrid
      loading={isLoading}
      rows={departments}
      columns={columns}
      toolbarProps={TOOLBAR_PROPS}
      pagination
      initialState={initialState}
      getRowId={(row) => row.id}
      disableAutosize
      sx={{
        '.no-line-height': {
          lineHeight: 'unset',
        },
      }}
    />
  );
};

export default DepartmentDataGrid;
