import { createHeaderName } from '@/components/base/data-display/datagrid/DataGrid.util';
import {
  getAuditColumn,
  getDescriptionColumn,
  getNameColumn,
  getStatusColumn,
} from '@/components/features/department/department-listing/department-data-grid/DepartmentDataGrid.util';
import transformText from '@/utils/text-transform.util';
import { GridActionsCellItem } from '@mui/x-data-grid-premium';
import type { CreateColumnsFn } from './DepartmentTemplateDataGrid.type';

/**
 * Creates the column definitions for the DepartmentTemplateDataGrid
 *
 * @param actions - Object of functions for creating actions (e.g., duplicateDepartmentTemplate)
 * @param theme - The current theme
 * @param t - Translation function
 * @returns Array of column definitions
 */
export const createColumns: CreateColumnsFn = ({ duplicateDepartmentTemplate }, _theme, t) => [
  getNameColumn(t),
  {
    field: 'scope',
    flex: 1,
    type: 'singleSelect',
    valueOptions: [
      {
        label: t('common.label.organisation'),
        value: 'organisation',
      },
      {
        label: t('common.label.merchant'),
        value: 'merchant',
      },
    ],
    groupable: false,
    headerName: createHeaderName('scope', t),
    renderCell: (params) => transformText(params.row.scope, 'capitalCase'),
  },
  getDescriptionColumn(t),
  getStatusColumn(t),
  getAuditColumn({
    t,
    type: 'created',
  }),
  getAuditColumn({
    t,
    type: 'updated',
  }),
  {
    field: 'actions',
    type: 'actions',
    description: createHeaderName('actions', t),
    flex: 0.1,
    hideable: false,
    width: 40,
    getActions: (params) => [
      <GridActionsCellItem
        key={params.id}
        label={transformText(t('common.action.duplicate'), 'sentenceCase')}
        showInMenu
        onClick={() => {
          duplicateDepartmentTemplate(params.row.id)();
        }}
      />,
    ],
  },
];
