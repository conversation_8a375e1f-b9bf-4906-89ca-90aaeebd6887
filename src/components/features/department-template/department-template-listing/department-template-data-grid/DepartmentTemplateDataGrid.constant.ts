import { PAGINATION_PAGE_SIZE } from '@/components/base/data-display/datagrid';

/**
 * Initial state for the DataGrid
 */
export const INITIAL_STATE = {
  pagination: {
    paginationModel: {
      pageSize: PAGINATION_PAGE_SIZE,
    },
  },
};

/**
 * Default toolbar props for the DataGrid
 */
export const TOOLBAR_PROPS = {
  showQuickFilter: true,
  showDensityButton: true,
  showColumnsButton: true,
  csvOptions: {
    fileName: 'department-templates',
  },
  quickFilterProps: {
    debounceMs: 500,
  },
};
