import { DataGrid } from '@/components/base/data-display/datagrid';
import { useTheme } from '@mui/material';
import type { GridRowId } from '@mui/x-data-grid-premium';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { INITIAL_STATE, TOOLBAR_PROPS } from './DepartmentTemplateDataGrid.constant';
import type { DepartmentTemplateDataGridProps } from './DepartmentTemplateDataGrid.type';
import { createColumns } from './DepartmentTemplateDataGrid.util';

/**
 * DepartmentTemplateDataGrid component displays department data in a grid with filtering, sorting, and pagination.
 *
 * @example
 * ```tsx
 * <DepartmentTemplateDataGrid
 *   departmentTemplates={departmentTemplates}
 *   isLoading={isLoading}
 *   onDuplicateDepartment={handleDuplicateDepartment}
 * />
 * ```
 */
export const DepartmentTemplateDataGrid = ({
  departmentTemplates,
  isLoading,
  onDuplicateDepartmentTemplate,
}: DepartmentTemplateDataGridProps) => {
  const theme = useTheme();
  const { t } = useTranslation();

  const duplicateDepartmentTemplate = useCallback(
    (id: GridRowId) => () => {
      if (onDuplicateDepartmentTemplate) {
        onDuplicateDepartmentTemplate(id);
      }
    },
    [onDuplicateDepartmentTemplate]
  );

  // Memoize columns definition to prevent recreation on every render
  const columns = useMemo(
    () =>
      createColumns(
        {
          duplicateDepartmentTemplate,
        },
        theme,
        t
      ),
    [duplicateDepartmentTemplate, theme, t]
  );

  // Memoize initial state to prevent recreation
  const initialState = useMemo(() => INITIAL_STATE, []);

  return (
    <DataGrid
      loading={isLoading}
      rows={departmentTemplates}
      columns={columns}
      toolbarProps={TOOLBAR_PROPS}
      pagination
      initialState={initialState}
      // rowHeight={65}
      getRowId={(row) => row.id}
      disableAutosize
      sx={{
        '.no-line-height': {
          lineHeight: 'unset',
        },
      }}
    />
  );
};

export default DepartmentTemplateDataGrid;
