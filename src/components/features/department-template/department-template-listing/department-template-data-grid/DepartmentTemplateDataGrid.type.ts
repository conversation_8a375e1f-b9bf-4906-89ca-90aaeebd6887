import type { DepartmentTemplate } from '@/services/department-templates';
import type { Theme } from '@mui/material';
import type { GridActionsColDef, GridColDef } from '@mui/x-data-grid-premium';
import type { TFunction } from 'i18next';

/**
 * Props for the DepartmentDataGrid component
 */
export interface DepartmentTemplateDataGridProps {
  /**
   * Array of department data to display in the grid
   */
  departmentTemplates: DepartmentTemplate[];

  /**
   * Whether the data is currently loading
   */
  isLoading: boolean;

  /**
   * Callback fired when a department template is duplicated
   *
   * If not provided, action will do nothing.
   * In a real application, this would typically make an API call to duplicate the department template.
   *
   * @param id - The ID of the department template to duplicate
   */
  onDuplicateDepartmentTemplate?: (id: string | number) => void;
}

export interface CreateColumnsActions {
  duplicateDepartmentTemplate: (id: string | number) => () => void;
}

export type CreateColumnsFn = (
  actions: CreateColumnsActions,
  theme: Theme,
  t: TFunction<'translation', undefined>
) => (GridColDef<DepartmentTemplate> | GridActionsColDef<DepartmentTemplate>)[];
